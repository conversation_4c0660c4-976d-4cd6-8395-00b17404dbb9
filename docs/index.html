<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AgentOrchestra: A Hierarchical Multi-Agent Framework for General-Purpose Task Solving</title>
  <meta name="description" content="AgentOrchestra is a hierarchical multi-agent framework for general-purpose task solving that integrates high-level planning with modular agent collaboration.">
  <meta name="keywords" content="AI, Agent, Multi-Agent, LLM, Deep Learning, Research">
  <meta name="author" content="<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://skyworkai.github.io/DeepResearchAgent/">
  <meta property="og:title" content="AgentOrchestra: A Hierarchical Multi-Agent Framework">
  <meta property="og:description" content="A hierarchical multi-agent framework for general-purpose task solving">
  <meta property="og:image" content="https://skyworkai.github.io/DeepResearchAgent/docs/assets/architecture.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://skyworkai.github.io/DeepResearchAgent/">
  <meta property="twitter:title" content="AgentOrchestra: A Hierarchical Multi-Agent Framework">
  <meta property="twitter:description" content="A hierarchical multi-agent framework for general-purpose task solving">
  <meta property="twitter:image" content="https://skyworkai.github.io/DeepResearchAgent/docs/assets/architecture.png">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#eff6ff',
              100: '#dbeafe',
              200: '#bfdbfe',
              300: '#93c5fd',
              400: '#60a5fa',
              500: '#3b82f6',
              600: '#2563eb',
              700: '#1d4ed8',
              800: '#1e40af',
              900: '#1e3a8a',
            },
            secondary: {
              50: '#f8fafc',
              100: '#f1f5f9',
              200: '#e2e8f0',
              300: '#cbd5e1',
              400: '#94a3b8',
              500: '#64748b',
              600: '#475569',
              700: '#334155',
              800: '#1e293b',
              900: '#0f172a',
            }
          },
          fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif'],
          },
          animation: {
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.5s ease-out',
            'float': 'float 6s ease-in-out infinite',
            'bounce-slow': 'bounce 2s infinite',
            'pulse-slow': 'pulse 3s infinite',
            'spin-slow': 'spin 3s linear infinite',
            'wiggle': 'wiggle 1s ease-in-out infinite',
            'gradient': 'gradient 3s ease infinite',
            'shimmer': 'shimmer 2s linear infinite',
            'scale-in': 'scaleIn 0.6s ease-out',
            'slide-in-left': 'slideInLeft 0.8s ease-out',
            'slide-in-right': 'slideInRight 0.8s ease-out',
            'rotate-in': 'rotateIn 0.8s ease-out',
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0' },
              '100%': { opacity: '1' },
            },
            slideUp: {
              '0%': { transform: 'translateY(20px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            },
            float: {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-20px)' },
            },
            wiggle: {
              '0%, 100%': { transform: 'rotate(-3deg)' },
              '50%': { transform: 'rotate(3deg)' },
            },
            gradient: {
              '0%, 100%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
            },
            shimmer: {
              '0%': { backgroundPosition: '-200% 0' },
              '100%': { backgroundPosition: '200% 0' },
            },
            scaleIn: {
              '0%': { transform: 'scale(0.8)', opacity: '0' },
              '100%': { transform: 'scale(1)', opacity: '1' },
            },
            slideInLeft: {
              '0%': { transform: 'translateX(-100px)', opacity: '0' },
              '100%': { transform: 'translateX(0)', opacity: '1' },
            },
            slideInRight: {
              '0%': { transform: 'translateX(100px)', opacity: '0' },
              '100%': { transform: 'translateX(0)', opacity: '1' },
            },
            rotateIn: {
              '0%': { transform: 'rotate(-180deg) scale(0.8)', opacity: '0' },
              '100%': { transform: 'rotate(0deg) scale(1)', opacity: '1' },
            }
          }
        }
      }
    }
  </script>
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- 页面加载动画 -->
  <style>
    .page-loader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
    }

    .loader-content {
      text-align: center;
      color: white;
    }

    .loader-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255,255,255,0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1.2s linear infinite;
      margin: 0 auto 20px;
    }

    .loader-text {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .loader-subtext {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 鼠标跟随效果 */
    .mouse-follower {
      position: fixed;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.6) 0%, rgba(59, 130, 246, 0.2) 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9998;
      transition: transform 0.1s ease, width 0.3s ease, height 0.3s ease;
      mix-blend-mode: screen;
    }

    .mouse-follower.hover {
      width: 40px;
      height: 40px;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, rgba(59, 130, 246, 0.3) 70%);
    }

    /* 滚动动画增强 */
    .scroll-animate {
      opacity: 0;
      transform: translateY(50px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .scroll-animate.animate {
      opacity: 1;
      transform: translateY(0);
    }

    .scroll-animate-left {
      opacity: 0;
      transform: translateX(-50px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .scroll-animate-left.animate {
      opacity: 1;
      transform: translateX(0);
    }

    .scroll-animate-right {
      opacity: 0;
      transform: translateX(50px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .scroll-animate-right.animate {
      opacity: 1;
      transform: translateX(0);
    }

    /* 3D 卡片效果 */
    .card-3d {
      transform-style: preserve-3d;
      transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .card-3d:hover {
      transform: rotateY(10deg) rotateX(5deg) translateZ(20px);
    }

    /* 渐变文字效果 */
    .gradient-text-animated {
      background: linear-gradient(-45deg, #3b82f6, #1d4ed8, #7c3aed, #3b82f6);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradient 3s ease infinite;
    }

    /* 按钮发光效果 */
    .glow-button {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .glow-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    .glow-button:hover::before {
      left: 100%;
    }

    .glow-button:hover {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
      transform: translateY(-2px);
    }

    /* 进度条动画 */
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #e5e7eb;
      border-radius: 4px;
      overflow: hidden;
      position: relative;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #3b82f6, #1d4ed8, #7c3aed);
      background-size: 200% 100%;
      border-radius: 4px;
      transform: translateX(-100%);
      transition: transform 2s cubic-bezier(0.4, 0, 0.2, 1);
      animation: gradient 3s ease infinite;
    }

    .progress-fill.animate {
      transform: translateX(0);
    }

    /* 粒子背景 */
    .particles-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: -1;
    }

    .particle {
      position: absolute;
      background: rgba(59, 130, 246, 0.1);
      border-radius: 50%;
      animation: float-particle 6s ease-in-out infinite;
    }

    @keyframes float-particle {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    /* 打字机光标效果 */
    .typewriter-cursor {
      border-right: 2px solid #3b82f6;
      animation: blink 1s infinite;
    }

    @keyframes blink {
      0%, 50% { border-color: transparent; }
      51%, 100% { border-color: #3b82f6; }
    }

    /* 悬停缩放效果 */
    .hover-scale {
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-scale:hover {
      transform: scale(1.05);
    }

    /* 波纹效果 */
    .ripple {
      position: relative;
      overflow: hidden;
    }

    .ripple::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      transition: width 0.6s, height 0.6s;
    }

    .ripple:active::after {
      width: 300px;
      height: 300px;
    }
  </style>
</head>

<body class="bg-gray-50 text-gray-900">
  <!-- 页面加载动画 -->
  <div id="pageLoader" class="page-loader">
    <div class="loader-content">
      <div class="loader-spinner"></div>
      <div class="loader-text">AgentOrchestra</div>
      <div class="loader-subtext">Loading amazing experiences...</div>
    </div>
  </div>

  <!-- 鼠标跟随效果 -->
  <div id="mouseFollower" class="mouse-follower"></div>

  <!-- 粒子背景 -->
  <div class="particles-bg" id="particlesBg"></div>

  <!-- Navigation -->
  <nav class="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50 transition-all duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-bold gradient-text-animated hover-scale">AgentOrchestra</h1>
        </div>
        <div class="flex items-center space-x-8">
          <a href="#introduction" class="text-gray-700 hover:text-primary-600 transition-colors duration-300 nav-link">Introduction</a>
          <a href="#architecture" class="text-gray-700 hover:text-primary-600 transition-colors duration-300 nav-link">Architecture</a>
          <a href="#mcp-manager" class="text-gray-700 hover:text-primary-600 transition-colors duration-300 nav-link">MCP Manager</a>
          <a href="#experiments" class="text-gray-700 hover:text-primary-600 transition-colors duration-300 nav-link">Experiments</a>
          <a href="#paper" class="text-gray-700 hover:text-primary-600 transition-colors duration-300 nav-link">Paper</a>
          <a href="https://github.com/SkyworkAI/DeepResearchAgent" target="_blank" class="glow-button bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-all duration-300">GitHub</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-50 to-blue-50 py-20 relative overflow-hidden">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
      <h1 class="text-5xl font-bold text-gray-900 mb-6 animate-scale-in typewriter-cursor" id="heroTitle">
        AgentOrchestra
      </h1>
      <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto animate-slide-up scroll-animate">
        A Hierarchical Multi-Agent Framework for General-Purpose Task Solving
      </p>
      <div class="flex justify-center space-x-4">
        <a href="#paper" class="glow-button ripple bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-all duration-300 animate-bounce-slow">
          Read Paper
        </a>
        <a href="https://github.com/SkyworkAI/DeepResearchAgent" target="_blank" class="glow-button ripple border border-primary-600 text-primary-600 px-8 py-3 rounded-lg hover:bg-primary-50 transition-all duration-300">
          View Code
        </a>
      </div>
    </div>
  </section>

  <!-- Introduction Section -->
  <section id="introduction" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 scroll-animate">
        <h2 class="text-3xl font-bold text-gray-900 mb-4 gradient-text-animated">1. Introduction</h2>
        <div class="w-24 h-1 bg-gradient-to-r from-primary-600 to-blue-600 mx-auto animate-pulse-slow"></div>
      </div>
      
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <div class="space-y-6 scroll-animate-left">
          <p class="text-lg text-gray-700 leading-relaxed">
            Recent advances in Large Language Models (LLMs) or Large Multimodal Models (LMMs) have led to a shift from simple dialogue to models capable of performing sophisticated reasoning, enabling progress from answering straightforward questions to responding to complex, multi-step queries.
          </p>
          <p class="text-lg text-gray-700 leading-relaxed">
            However, current LLMs remain largely disconnected from real-world environments due to the absence of interactive tool integration, which constrains their ability to perform grounded, general-purpose, and complex tasks.
          </p>
          <p class="text-lg text-gray-700 leading-relaxed">
            AgentOrchestra addresses these challenges through a hierarchical multi-agent framework that integrates high-level planning with modular agent collaboration, inspired by the way a conductor orchestrates a symphony.
          </p>
        </div>
        <div class="bg-gradient-to-br from-primary-100 to-blue-100 p-8 rounded-2xl card-3d hover-scale scroll-animate-right">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Key Principles</h3>
          <ul class="space-y-3">
            <li class="flex items-center animate-slide-up" style="animation-delay: 0.1s;">
              <div class="w-2 h-2 bg-primary-600 rounded-full mr-3 animate-pulse"></div>
              <span class="text-gray-700">Extensibility</span>
            </li>
            <li class="flex items-center animate-slide-up" style="animation-delay: 0.2s;">
              <div class="w-2 h-2 bg-primary-600 rounded-full mr-3 animate-pulse"></div>
              <span class="text-gray-700">Multimodality</span>
            </li>
            <li class="flex items-center animate-slide-up" style="animation-delay: 0.3s;">
              <div class="w-2 h-2 bg-primary-600 rounded-full mr-3 animate-pulse"></div>
              <span class="text-gray-700">Modularity</span>
            </li>
            <li class="flex items-center animate-slide-up" style="animation-delay: 0.4s;">
              <div class="w-2 h-2 bg-primary-600 rounded-full mr-3 animate-pulse"></div>
              <span class="text-gray-700">Coordination</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- Architecture Section -->
  <section id="architecture" class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 scroll-animate">
        <h2 class="text-3xl font-bold text-gray-900 mb-4 gradient-text-animated">2. Architecture</h2>
        <div class="w-24 h-1 bg-gradient-to-r from-primary-600 to-blue-600 mx-auto animate-pulse-slow"></div>
      </div>
      
      <!-- Architecture Diagram -->
      <div class="text-center mb-16 scroll-animate">
        <img src="assets/architecture.png" alt="AgentOrchestra Architecture" class="max-w-4xl mx-auto rounded-lg shadow-lg animate-float hover-scale">
      </div>
      
      <!-- Architecture Components -->
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Planning Agent -->
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer card-3d hover-scale scroll-animate" onclick="togglePlanningFlowchart()">
          <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4 animate-bounce-slow">
            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Planning Agent</h3>
          <p class="text-gray-600">Central coordinator that decomposes complex objectives and delegates sub-tasks to specialized agents.</p>
          <div class="mt-3 text-primary-600 text-sm font-medium animate-pulse">Click to view workflow →</div>
        </div>
        
        <!-- Deep Researcher -->
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer card-3d hover-scale scroll-animate" style="animation-delay: 0.1s;" onclick="toggleDeepResearcherFlowchart()">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 animate-bounce-slow">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Deep Researcher</h3>
          <p class="text-gray-600">Conducts thorough research on specified topics, retrieving and synthesizing high-quality information.</p>
          <div class="mt-3 text-green-600 text-sm font-medium animate-pulse">Click to view workflow →</div>
        </div>
        
        <!-- Browser Use -->
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer card-3d hover-scale scroll-animate" style="animation-delay: 0.2s;" onclick="toggleBrowserUseFlowchart()">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 animate-bounce-slow">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Browser Use</h3>
          <p class="text-gray-600">Automates browser operations, supporting web search, information extraction, and data collection.</p>
          <div class="mt-3 text-blue-600 text-sm font-medium animate-pulse">Click to view workflow →</div>
        </div>
        
        <!-- Deep Analyzer -->
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer card-3d hover-scale scroll-animate" style="animation-delay: 0.3s;" onclick="toggleDeepAnalyzerFlowchart()">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 animate-bounce-slow">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Deep Analyzer</h3>
          <p class="text-gray-600">Performs in-depth analysis of input information, extracting key insights and potential requirements.</p>
          <div class="mt-3 text-purple-600 text-sm font-medium animate-pulse">Click to view workflow →</div>
        </div>
        
        <!-- General Tool Calling -->
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer card-3d hover-scale scroll-animate" style="animation-delay: 0.4s;" onclick="toggleGeneralToolCallingFlowchart()">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4 animate-bounce-slow">
            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">General Tool Calling</h3>
          <p class="text-gray-600">Provides a general-purpose interface for invoking various tools and APIs with function calling support.</p>
          <div class="mt-3 text-orange-600 text-sm font-medium animate-pulse">Click to view workflow →</div>
        </div>
        
        <!-- MCP Manager Agent -->
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer card-3d hover-scale scroll-animate" style="animation-delay: 0.5s;" onclick="toggleMCPFlowchart()">
          <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4 animate-bounce-slow">
            <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">MCP Manager Agent</h3>
          <p class="text-gray-600">Enables intelligent tool evolution through automated creation, dynamic retrieval, and systematic reuse of MCP tools.</p>
          <div class="mt-3 text-teal-600 text-sm font-medium animate-pulse">Click to view workflow →</div>
        </div>
      </div>
    </div>
  </section>

  <!-- MCP Manager Agent Section -->
  <section id="mcp-manager" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">MCP Manager Agent</h2>
        <div class="w-24 h-1 bg-primary-600 mx-auto"></div>
      </div>
      
      <div class="grid lg:grid-cols-2 gap-12 items-start">
        <div class="space-y-6">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Problem Statement</h3>
            <p class="text-gray-700 leading-relaxed">
              The rapid expansion of AI agent applications has led to exponential growth in the complexity and diversity of required Model Context Protocol (MCP) tools. Traditional approaches relying on manual tool development face significant challenges including development inefficiency, version inconsistency, and limited adaptability to emerging requirements.
            </p>
          </div>
          
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Solution</h3>
            <p class="text-gray-700 leading-relaxed">
              The MCP Manager Agent addresses these limitations through intelligent tool evolution via automated creation, dynamic retrieval, and systematic reuse mechanisms. This represents a paradigm shift from static tool provisioning to adaptive tool ecosystem management.
            </p>
          </div>
        </div>
        
        <div class="bg-gradient-to-br from-teal-50 to-cyan-50 p-8 rounded-2xl">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Core Capabilities</h3>
          <div class="space-y-4">
            <div class="flex items-start">
              <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3 mt-1">
                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">Tool Retrieval</h4>
                <p class="text-sm text-gray-600">Keyword pre-filtering strategy to efficiently match tasks with relevant tools from the library.</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3 mt-1">
                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">Tool Creation</h4>
                <p class="text-sm text-gray-600">Automated generation of MCP-compliant tools through intent analysis, synthesis, and validation phases.</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3 mt-1">
                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">Tool Reuse</h4>
                <p class="text-sm text-gray-600">Comprehensive tool registry with persistence, versioning, and lifecycle tracking capabilities.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Workflow Process -->
      <div class="mt-16">
        <h3 class="text-2xl font-semibold text-gray-900 mb-8 text-center">Tool Creation Workflow</h3>
        <div class="grid md:grid-cols-4 gap-6">
          <div class="bg-white p-6 rounded-xl shadow-sm border-l-4 border-blue-500">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <span class="text-blue-600 font-bold">1</span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2 text-center">Intent Analysis</h4>
            <p class="text-gray-600 text-sm text-center">Parse user task intentions and extract functional requirements, input-output specifications, and operational constraints.</p>
          </div>
          
          <div class="bg-white p-6 rounded-xl shadow-sm border-l-4 border-green-500">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <span class="text-green-600 font-bold">2</span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2 text-center">Tool Synthesis</h4>
            <p class="text-gray-600 text-sm text-center">Generate executable MCP-compliant tool implementations with parameterized scripts and error handling.</p>
          </div>
          
          <div class="bg-white p-6 rounded-xl shadow-sm border-l-4 border-purple-500">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <span class="text-purple-600 font-bold">3</span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2 text-center">Validation</h4>
            <p class="text-gray-600 text-sm text-center">Multi-stage evaluation protocol assessing tool correctness, performance characteristics, and integration compatibility.</p>
          </div>
          
          <div class="bg-white p-6 rounded-xl shadow-sm border-l-4 border-orange-500">
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <span class="text-orange-600 font-bold">4</span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2 text-center">Registration</h4>
            <p class="text-gray-600 text-sm text-center">Register validated tools in the system's tool registry with comprehensive metadata and usage examples.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Experiments Section -->
  <section id="experiments" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">3. Experiments</h2>
        <div class="w-24 h-1 bg-primary-600 mx-auto"></div>
      </div>
      
      <!-- GAIA Benchmark Chart -->
      <div class="mb-16">
        <h3 class="text-2xl font-semibold text-gray-900 mb-8 text-center">GAIA Benchmark Test Results</h3>
        <div class="bg-white p-8 rounded-xl shadow-sm">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-gradient-to-r from-blue-400 to-cyan-400 rounded"></div>
                <span class="text-sm text-gray-600">AgentOrchestra (Our)</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-4 h-4 bg-gray-400 rounded"></div>
                <span class="text-sm text-gray-600">Other Models</span>
              </div>
            </div>
          </div>
          
          <div class="space-y-4">
            <!-- Skywork DRA -->
            <div class="flex items-center chart-row" data-score="83.39">
              <div class="w-48 text-sm text-gray-600">AgentOrchestra</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gradient-to-r from-blue-400 to-cyan-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="97.6">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">83.06</span>
                </div>
              </div>
            </div>
            
            <!-- Skywork DRA (w/o MCP) -->
            <div class="flex items-center chart-row" data-score="79.07">
              <div class="w-48 text-sm text-gray-600">AgentOrchestra (w/o MCP)</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gradient-to-r from-blue-400 to-cyan-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="93.0">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">79.07</span>
                </div>
              </div>
            </div>
            
            <!-- Aworld -->
            <div class="flex items-center chart-row" data-score="81.73">
              <div class="w-48 text-sm text-gray-600">Aworld</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="96.1">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">81.73</span>
                </div>
              </div>
            </div>
            
            <!-- Su Zero Ultra -->
            <div class="flex items-center chart-row" data-score="80.40">
              <div class="w-48 text-sm text-gray-600">Su Zero Ultra</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="94.6">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">80.40</span>
                </div>
              </div>
            </div>
            
            <!-- h2oGPTe Agent -->
            <div class="flex items-center chart-row" data-score="79.73">
              <div class="w-48 text-sm text-gray-600">h2oGPTe Agent</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="93.8">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">79.73</span>
                </div>
              </div>
            </div>
            
            <!-- desearch -->
            <div class="flex items-center chart-row" data-score="78.07">
              <div class="w-48 text-sm text-gray-600">desearch</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="91.8">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">78.07</span>
                </div>
              </div>
            </div>
            
            <!-- Alita -->
            <div class="flex items-center chart-row" data-score="75.42">
              <div class="w-48 text-sm text-gray-600">Alita</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="88.7">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">75.42</span>
                </div>
              </div>
            </div>
            
            <!-- Langfun Agent -->
            <div class="flex items-center chart-row" data-score="73.09">
              <div class="w-48 text-sm text-gray-600">Langfun Agent</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="85.9">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">73.09</span>
                </div>
              </div>
            </div>
            
            <!-- o3-deep-research -->
            <div class="flex items-center chart-row" data-score="68.67">
              <div class="w-48 text-sm text-gray-600">o3-deep-research</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="80.8">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">68.67</span>
                </div>
              </div>
            </div>
            
            <!-- JoyAgent-Genie -->
            <div class="flex items-center chart-row" data-score="65.12">
              <div class="w-48 text-sm text-gray-600">JoyAgent-Genie</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="76.6">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">65.12</span>
                </div>
              </div>
            </div>
            
            <!-- o4-mini-deep-research -->
            <div class="flex items-center chart-row" data-score="59.33">
              <div class="w-48 text-sm text-gray-600">o4-mini-deep-research</div>
              <div class="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div class="chart-bar bg-gray-400 h-8 rounded-full flex items-center justify-end pr-3 transition-all duration-2000 ease-out" data-width="69.8">
                  <span class="chart-value text-white font-semibold text-sm opacity-0">59.33</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Benchmark Summary -->
      <div class="grid lg:grid-cols-3 gap-8 mb-16">
        <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">SimpleQA Benchmark</h3>
          <p class="text-gray-600 mb-4">Evaluation on simple question-answering tasks to assess basic reasoning capabilities.</p>
          <div class="text-2xl font-bold text-green-600">95.3</div>
        </div>
        
        <div class="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-xl">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">GAIA Benchmark Validation</h3>
          <p class="text-gray-600 mb-4">Comprehensive evaluation on real-world tasks requiring web search and reasoning.</p>
          <div class="text-2xl font-bold text-blue-600">82.42</div>
        </div>
        
        <div class="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-xl">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">HLE Benchmark</h3>
          <p class="text-gray-600 mb-4">Human-level evaluation benchmark for complex reasoning and planning tasks.</p>
          <div class="text-2xl font-bold text-purple-600">25.9</div>
        </div>
      </div>
      
      <!-- Results -->
      <div class="bg-gray-50 p-8 rounded-xl">
        <h3 class="text-2xl font-semibold text-gray-900 mb-6">Key Results</h3>
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h4 class="text-lg font-semibold text-gray-900 mb-3">Performance Improvements</h4>
            <ul class="space-y-2 text-gray-600">
              <li>• Consistently outperforms flat-agent and monolithic baselines</li>
              <li>• Superior task success rate and adaptability</li>
              <li>• Effective hierarchical organization and role specialization</li>
            </ul>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900 mb-3">Scalability Benefits</h4>
            <ul class="space-y-2 text-gray-600">
              <li>• Modular design enables easy integration of new agents</li>
              <li>• Flexible orchestration through explicit sub-goal formulation</li>
              <li>• Adaptive role allocation for dynamic task requirements</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Paper Section -->
  <section id="paper" class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Paper & Resources</h2>
        <div class="w-24 h-1 bg-primary-600 mx-auto"></div>
      </div>
      
      <div class="grid md:grid-cols-2 gap-8">
        <div class="bg-white p-8 rounded-xl shadow-sm">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Research Paper</h3>
          <p class="text-gray-600 mb-6">
            Read our full paper "AgentOrchestra: A Hierarchical Multi-Agent Framework for General-Purpose Task Solving" published on arXiv.
          </p>
          <div class="space-y-3">
            <a href="https://arxiv.org/abs/2506.12508" target="_blank" class="block bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors text-center">
              View on arXiv
            </a>
            <a href="https://arxiv.org/pdf/2506.12508" target="_blank" class="block border border-primary-600 text-primary-600 px-6 py-3 rounded-lg hover:bg-primary-50 transition-colors text-center">
              Download PDF
            </a>
          </div>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-sm">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Code Repository</h3>
          <p class="text-gray-600 mb-6">
            Access the complete implementation, examples, and documentation on GitHub.
          </p>
          <div class="space-y-3">
            <a href="https://github.com/SkyworkAI/DeepResearchAgent" target="_blank" class="block bg-gray-900 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors text-center">
              View on GitHub
            </a>
            <a href="https://github.com/SkyworkAI/DeepResearchAgent/blob/main/README.md" target="_blank" class="block border border-gray-600 text-gray-600 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors text-center">
              Documentation
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Authors Section -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Authors</h2>
        <div class="w-24 h-1 bg-primary-600 mx-auto"></div>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="w-20 h-20 bg-primary-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-primary-600 font-semibold">WZ</span>
          </div>
          <h3 class="font-semibold text-gray-900">Wentao Zhang</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-teal-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-teal-600 font-semibold">LZ</span>
          </div>
          <h3 class="font-semibold text-gray-900">Liang Zeng</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-cyan-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-cyan-600 font-semibold">YX</span>
          </div>
          <h3 class="font-semibold text-gray-900">Yuzhen Xiao</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-emerald-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-emerald-600 font-semibold">YCL</span>
          </div>
          <h3 class="font-semibold text-gray-900">Yongcong Li</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-green-600 font-semibold">CC</span>
          </div>
          <h3 class="font-semibold text-gray-900">Ce Cui</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-blue-600 font-semibold">YZ</span>
          </div>
          <h3 class="font-semibold text-gray-900">Yilei Zhao</h3>
          <p class="text-gray-600 text-sm">Nanyang Technological University</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-orange-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-orange-600 font-semibold">RH</span>
          </div>
          <h3 class="font-semibold text-gray-900">Rui Hu</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-purple-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-purple-600 font-semibold">YL</span>
          </div>
          <h3 class="font-semibold text-gray-900">Yang Liu</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-pink-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-pink-600 font-semibold">YHZ</span>
          </div>
          <h3 class="font-semibold text-gray-900">Yahui Zhou</h3>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
        
        <div class="text-center">
          <div class="w-20 h-20 bg-indigo-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-indigo-600 font-semibold">BA</span>
          </div>
          <h3 class="font-semibold text-gray-900">Bo An</h3>
          <p class="text-gray-600 text-sm">Nanyang Technological University</p>
          <p class="text-gray-600 text-sm">Skywork AI</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-xl font-bold mb-4">AgentOrchestra</h3>
          <p class="text-gray-400">
            A hierarchical multi-agent framework for general-purpose task solving that integrates high-level planning with modular agent collaboration.
          </p>
        </div>
        
        <div>
          <h4 class="font-semibold mb-4">Quick Links</h4>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#introduction" class="hover:text-white transition-colors">Introduction</a></li>
            <li><a href="#architecture" class="hover:text-white transition-colors">Architecture</a></li>
            <li><a href="#mcp-manager" class="hover:text-white transition-colors">MCP Manager</a></li>
            <li><a href="#experiments" class="hover:text-white transition-colors">Experiments</a></li>
            <li><a href="#paper" class="hover:text-white transition-colors">Paper</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-semibold mb-4">Resources</h4>
          <ul class="space-y-2 text-gray-400">
            <li><a href="https://arxiv.org/abs/2506.12508" target="_blank" class="hover:text-white transition-colors">arXiv Paper</a></li>
            <li><a href="https://github.com/SkyworkAI/DeepResearchAgent" target="_blank" class="hover:text-white transition-colors">GitHub Repository</a></li>
            <li><a href="https://github.com/SkyworkAI/DeepResearchAgent/blob/main/README.md" target="_blank" class="hover:text-white transition-colors">Documentation</a></li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
        <p>&copy; 2025 AgentOrchestra. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- MCP Manager Agent Flowchart Modal -->
  <div id="mcpModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <div style="color: #111827; font-size: 1.5rem; font-weight: 700; margin: 0; padding: 0; font-family: system-ui, sans-serif;">MCP Manager Agent Workflow</div>
        <button onclick="closeMCPModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="p-6">
        <div class="flowchart-container bg-gradient-to-br from-teal-50 to-cyan-50 rounded-xl p-8">
          <div class="flowchart">
            <!-- Row 1: Start -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Task Input
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 2: Requirements Analysis -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/>
                  </svg>
                  Analyze MCP Tool Requirements
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 3: Decision Point -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Are Existing MCP Tools Available?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 4: Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                  </svg>
                  Filter Required MCP Tools from Repository
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                  </svg>
                  Create Missing MCP Tools
                </div>
              </div>
            </div>
            
            <!-- Row 5: Tool Creation Flow -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  MCP Tool Evaluation
                </div>
              </div>
            </div>
            
            <!-- Row 6: Evaluation Decision -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">PASS</div>
                </div>
                <div class="flow-content success">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  Add to Tool Registry
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">FAIL</div>
                </div>
                <div class="flow-content failure">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                  Discard Tool
                </div>
              </div>
            </div>
            
            <!-- Row 7: Execution -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                  Execute MCP Tools
                </div>
              </div>
            </div>
            
            <!-- Row 8: Completion -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Task Completed
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Planning Agent Flowchart Modal -->
  <div id="planningModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <div style="color: #111827; font-size: 1.5rem; font-weight: 700; margin: 0; padding: 0; font-family: system-ui, sans-serif;">Planning Agent Workflow</div>
        <button onclick="closePlanningModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="p-6">
        <div class="flowchart-container bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8">
          <div class="flowchart">
            <!-- Row 1: Start -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  User Objective Input
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 2: Objective Analysis -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/>
                  </svg>
                  Analyze User Objective & Context
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 3: Task Decomposition -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                  </svg>
                  Decompose Complex Task into Sub-tasks
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 4: Plan Creation -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                  Create Execution Plan with Steps
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 5: Agent Assignment -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  Assign Sub-tasks to Specialized Agents
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 6: Execution Monitoring -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Monitor Execution Progress & Collect Feedback
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 7: Decision Point -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Need Plan Adaptation?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 8: Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                  </svg>
                  Update Plan & Reassign Tasks
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Continue Current Execution
                </div>
              </div>
            </div>
            
            <!-- Row 9: Progress Check -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content decision">
                  <span>All Tasks Completed?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 10: Final Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content success">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  Aggregate Results & Complete
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  Return to Monitoring
                </div>
              </div>
            </div>
            
            <!-- Row 11: Completion -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Objective Achieved
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Deep Researcher Agent Flowchart Modal -->
  <div id="deepResearcherModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <div style="color: #111827; font-size: 1.5rem; font-weight: 700; margin: 0; padding: 0; font-family: system-ui, sans-serif;">Deep Researcher Agent Workflow</div>
        <button onclick="closeDeepResearcherModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="p-6">
        <div class="flowchart-container bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-8">
          <div class="flowchart">
            <!-- Row 1: Start -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Research Query Input
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 2: Query Optimization -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/>
                  </svg>
                  Optimize Query with LLM
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 3: Multi-Engine Search -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                  </svg>
                  Breadth-First Search Across Multiple Engines
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 4: Content Analysis -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                  </svg>
                  Extract Key Insights & Assign Relevance Scores
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 5: Follow-up Generation -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                  </svg>
                  Generate Follow-up Queries
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 6: Depth Check -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Reached Depth/Time Limit?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 7: Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Proceed to Synthesis
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  Continue Recursive Search
                </div>
              </div>
            </div>
            
            <!-- Row 8: Python Processing Decision -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content decision">
                  <span>Need Data Processing?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 9: Python Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                  </svg>
                  Python Interpreter for Data Processing
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Skip Data Processing
                </div>
              </div>
            </div>
            
            <!-- Row 10: Synthesis -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content success">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  Generate Structured Summary with Citations
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 10: Completion -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Research Complete
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Browser Use Agent Flowchart Modal -->
  <div id="browserUseModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <div style="color: #111827; font-size: 1.5rem; font-weight: 700; margin: 0; padding: 0; font-family: system-ui, sans-serif;">Browser Use Agent Workflow</div>
        <button onclick="closeBrowserUseModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="p-6">
        <div class="flowchart-container bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-8">
          <div class="flowchart">
            <!-- Row 1: Start -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Browser Task Request
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 2: Action Registry -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                  </svg>
                  Parse Action from Central Registry
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 3: Session Management -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  Initialize Browser Session & State Management
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 4: Action Execution -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                  Execute Parameterized Browser Action
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 5: Action Types -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Action Type?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 6: Action Categories -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">Navigation</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
                  </svg>
                  URL Navigation & Tab Management
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">Interaction</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5-7-7 4-11-2 5z"/>
                  </svg>
                  DOM Manipulation & Form Filling
                </div>
              </div>
            </div>
            
            <!-- Row 7: Content Processing -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Content Extraction & Media Control
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 8: Error Handling -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Execution Successful?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 9: Error Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content success">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  Return Results & Update State
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content failure">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                  Handle Error & Retry Logic
                </div>
              </div>
            </div>
            
            <!-- Row 10: Python Integration Decision -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content decision">
                  <span>Need Advanced Scripting?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 11: Python Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                  </svg>
                  Python Interpreter for Advanced Scripting
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Skip Python Processing
                </div>
              </div>
            </div>
            
            <!-- Row 12: Completion -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Task Completed
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Deep Analyzer Agent Flowchart Modal -->
  <div id="deepAnalyzerModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <div style="color: #111827; font-size: 1.5rem; font-weight: 700; margin: 0; padding: 0; font-family: system-ui, sans-serif;">Deep Analyzer Agent Workflow</div>
        <button onclick="closeDeepAnalyzerModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="p-6">
        <div class="flowchart-container bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-8">
          <div class="flowchart">
            <!-- Row 1: Start -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Analysis Task & Source Materials
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 2: Data Format Detection -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/>
                  </svg>
                  Detect & Extract Multi-format Data
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 3: Data Types -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Data Type?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 4: Data Type Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">Text/Image</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                  </svg>
                  Process Text & Visual Content
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">Audio/Video</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                  Process Audio & Video Content
                </div>
              </div>
            </div>
            
            <!-- Row 5: Content Structuring -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                  </svg>
                  Structure Content for Analysis
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 6: LLM Analysis -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                  </svg>
                  Step-by-step LLM Reasoning
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 7: Multi-Model Synthesis -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Multiple Models?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 8: Synthesis Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                  </svg>
                  Synthesize Results from Multiple Models
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Use Single Model Results
                </div>
              </div>
            </div>
            
            <!-- Row 9: Python Processing Decision -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content decision">
                  <span>Need Custom Analysis?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 10: Python Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                  </svg>
                  Python Interpreter for Custom Analysis
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Skip Custom Analysis
                </div>
              </div>
            </div>
            
            <!-- Row 11: Summarization -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content success">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  Generate Coherent Analysis Summary
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 11: Completion -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Analysis Complete
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- General Tool Calling Agent Flowchart Modal -->
  <div id="generalToolCallingModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <div style="color: #111827; font-size: 1.5rem; font-weight: 700; margin: 0; padding: 0; font-family: system-ui, sans-serif;">General Tool Calling Agent Workflow</div>
        <button onclick="closeGeneralToolCallingModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="p-6">
        <div class="flowchart-container bg-gradient-to-br from-orange-50 to-amber-50 rounded-xl p-8">
          <div class="flowchart">
            <!-- Row 1: Start -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Tool Call Request
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 2: Function Analysis -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/>
                  </svg>
                  Parse Function Call & Parameters
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 3: Tool Registry -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content process">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                  </svg>
                  Lookup Tool in Registry
                </div>
              </div>
            </div>
            
            <!-- Vertical Arrow -->
            <div class="flow-arrow">
              <svg viewBox="0 0 24 24">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </div>
            
            <!-- Row 4: Tool Found Check -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-content decision">
                  <span>Tool Found?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 5: Tool Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Validate Parameters
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content failure">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                  Return Tool Not Found Error
                </div>
              </div>
            </div>
            
            <!-- Row 6: Parameter Validation -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content decision">
                  <span>Parameters Valid?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 7: Validation Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content action">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                  Execute Tool Function
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content failure">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                  Return Parameter Error
                </div>
              </div>
            </div>
            
            <!-- Row 8: Execution Result -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content decision">
                  <span>Execution Successful?</span>
                </div>
              </div>
            </div>
            
            <!-- Row 9: Execution Branch -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label yes">YES</div>
                </div>
                <div class="flow-content success">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  Return Tool Results
                </div>
              </div>
              
              <div class="flow-node">
                <div class="flow-arrow-horizontal">
                  <svg viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                  </svg>
                  <div class="arrow-label no">NO</div>
                </div>
                <div class="flow-content failure">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                  Return Execution Error
                </div>
              </div>
            </div>
            
            <!-- Row 10: Completion -->
            <div class="flow-row">
              <div class="flow-node">
                <div class="flow-arrow">
                  <svg viewBox="0 0 24 24">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                  </svg>
                </div>
                <div class="flow-content start-end">
                  <svg class="flow-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Tool Call Complete
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Custom JavaScript -->
  <script src="script.js"></script>
  
  <style>
    /* Flowchart Styles */
    .flowchart-container {
      font-family: 'Inter', system-ui, sans-serif;
    }
    
    .flowchart {
      display: flex;
      flex-direction: column;
      gap: 20px;
      align-items: center;
    }
    
    .flow-row {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 30px;
      flex-wrap: wrap;
    }
    
    .flow-node {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      position: relative;
    }
    
    .flow-content {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 16px 24px;
      border-radius: 10px;
      font-weight: 600;
      font-size: 13px;
      color: white;
      box-shadow: 0 6px 12px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
      min-width: 200px;
      text-align: center;
      line-height: 1.4;
    }
    
    .flow-content:hover {
      transform: translateY(-3px);
    }
    
    .start-end {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 40px;
      padding: 18px 36px;
    }
    
    .process {
      background: linear-gradient(135deg, #4facfe, #00f2fe);
    }
    
    .decision {
      background: linear-gradient(135deg, #fa709a, #fee140);
      transform: rotate(45deg);
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .decision span {
      transform: rotate(-45deg);
      font-size: 12px;
      line-height: 1.3;
      text-align: center;
    }
    
    .action {
      background: linear-gradient(135deg, #a8edea, #fed6e3);
      color: #2c3e50;
    }
    
    .success {
      background: linear-gradient(135deg, #56ab2f, #a8e6cf);
      color: white;
    }
    
    .failure {
      background: linear-gradient(135deg, #ff416c, #ff4b2b);
      color: white;
    }
    
    .flow-arrow {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .flow-arrow svg {
      width: 30px;
      height: 30px;
      fill: #667eea;
    }
    
    .flow-arrow-horizontal {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    
    .flow-arrow-horizontal svg {
      width: 35px;
      height: 35px;
      fill: #667eea;
    }
    
    .arrow-label {
      position: absolute;
      background: #667eea;
      color: white;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 10px;
      font-weight: bold;
      white-space: nowrap;
    }
    
    .arrow-label.yes {
      top: -12px;
      right: -12px;
    }
    
    .arrow-label.no {
      bottom: -12px;
      left: -12px;
    }
    
    .flow-icon {
      width: 20px;
      height: 20px;
      fill: currentColor;
      flex-shrink: 0;
    }
  </style>
  
  <script>
    function toggleMCPFlowchart() {
      const modal = document.getElementById('mcpModal');
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
    
    function closeMCPModal() {
      const modal = document.getElementById('mcpModal');
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
    
    // Close modal when clicking outside
    document.getElementById('mcpModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeMCPModal();
      }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeMCPModal();
        closePlanningModal();
        closeDeepResearcherModal();
        closeBrowserUseModal();
        closeDeepAnalyzerModal();
        closeGeneralToolCallingModal();
      }
    });
    
    // Planning Agent Modal Functions
    function togglePlanningFlowchart() {
      const modal = document.getElementById('planningModal');
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
    
    function closePlanningModal() {
      const modal = document.getElementById('planningModal');
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
    
    // Close planning modal when clicking outside
    document.getElementById('planningModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closePlanningModal();
      }
    });
    
    // Deep Researcher Agent Modal Functions
    function toggleDeepResearcherFlowchart() {
      const modal = document.getElementById('deepResearcherModal');
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
    
    function closeDeepResearcherModal() {
      const modal = document.getElementById('deepResearcherModal');
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
    
    // Close deep researcher modal when clicking outside
    document.getElementById('deepResearcherModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeDeepResearcherModal();
      }
    });
    
    // Browser Use Agent Modal Functions
    function toggleBrowserUseFlowchart() {
      const modal = document.getElementById('browserUseModal');
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
    
    function closeBrowserUseModal() {
      const modal = document.getElementById('browserUseModal');
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
    
    // Close browser use modal when clicking outside
    document.getElementById('browserUseModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeBrowserUseModal();
      }
    });
    
    // Deep Analyzer Agent Modal Functions
    function toggleDeepAnalyzerFlowchart() {
      const modal = document.getElementById('deepAnalyzerModal');
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
    
    function closeDeepAnalyzerModal() {
      const modal = document.getElementById('deepAnalyzerModal');
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
    
    // Close deep analyzer modal when clicking outside
    document.getElementById('deepAnalyzerModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeDeepAnalyzerModal();
      }
    });
    
    // General Tool Calling Agent Modal Functions
    function toggleGeneralToolCallingFlowchart() {
      const modal = document.getElementById('generalToolCallingModal');
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
    
    function closeGeneralToolCallingModal() {
      const modal = document.getElementById('generalToolCallingModal');
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
    
    // Close general tool calling modal when clicking outside
    document.getElementById('generalToolCallingModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeGeneralToolCallingModal();
      }
    });
  </script>
</body>
</html>
