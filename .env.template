PYTHONWARNINGS=ignore # ignore warnings
ANONYMIZED_TELEMETRY=false # disable telemetry

# Hugging Face API Key
HUGGINEFACE_API_KEY=xxxxxx # Hugging Face API Base URL, you can get it from https://huggingface.co

# Firecrawl API Key
FIRECRAWL_API_KEY=xxxxxx # Firecrawl API Base URL, you can get it from https://www.firecrawl.dev

# OpenAI API Key
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=xxxxxx
# Azure OpenAI API Key
AZURE_OPENAI_API_BASE=xxxxx
AZURE_OPENAI_API_VERSION=xxxxx
AZURE_OPENAI_API_KEY=xxxxxx
# Anthropic API Key
ANTHROPIC_API_BASE=https://api.anthropic.com
ANTHROPIC_API_KEY=xxxxxx
# Google API Key
GOOGLE_APPLICATION_CREDENTIALS=your/root/path/.config/gcloud/application_default_credentials.json
GOOGLE_API_BASE=https://generativelanguage.googleapis.com
GOOGLE_API_KEY=xxxxxx
# Qwen API Key
QWEN_API_BASE=http://localhost:8000/v1
QWEN_API_KEY=xxxxxx
QWEN_VL_API_BASE=http://localhost:8000/v1
QWEN_VL_API_KEY=xxxxxx

# Local Proxy (It is only used for skywork local testing, you can ignore it)
# LOCAL_PROXY_BASE=http://localhost:6655
SKYWORK_API_BASE=xxxxx
SKYWORK_OPENROUTER_BJ_API_BASE=xxxxx
SKYWORK_OPENROUTER_US_API_BASE=xxxxx
SKYWORK_AZURE_HK_API_BASE=xxxxx
SKYWORK_AZURE_US_API_BASE=xxxxx
SKYWORK_AZURE_BJ_API_BASE=xxxxx
SKYWORK_GOOGLE_API_BASE=xxxxx
SKYWORK_API_KEY=xxxxx
SKYWORK_GOOGLE_SEARCH_API=xxxxx