<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JSHint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="463">
            <item index="0" class="java.lang.String" itemvalue="google-pasta" />
            <item index="1" class="java.lang.String" itemvalue="bs4" />
            <item index="2" class="java.lang.String" itemvalue="tensorflow-estimator" />
            <item index="3" class="java.lang.String" itemvalue="pytorch-warmup" />
            <item index="4" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="5" class="java.lang.String" itemvalue="tabulate" />
            <item index="6" class="java.lang.String" itemvalue="dm-control" />
            <item index="7" class="java.lang.String" itemvalue="multitasking" />
            <item index="8" class="java.lang.String" itemvalue="srsly" />
            <item index="9" class="java.lang.String" itemvalue="pyasn1-modules" />
            <item index="10" class="java.lang.String" itemvalue="ftfy" />
            <item index="11" class="java.lang.String" itemvalue="dm-env" />
            <item index="12" class="java.lang.String" itemvalue="astunparse" />
            <item index="13" class="java.lang.String" itemvalue="lxml" />
            <item index="14" class="java.lang.String" itemvalue="multiprocess" />
            <item index="15" class="java.lang.String" itemvalue="soupsieve" />
            <item index="16" class="java.lang.String" itemvalue="mmcv-full" />
            <item index="17" class="java.lang.String" itemvalue="mmcv" />
            <item index="18" class="java.lang.String" itemvalue="pywin32" />
            <item index="19" class="java.lang.String" itemvalue="libclang" />
            <item index="20" class="java.lang.String" itemvalue="portalocker" />
            <item index="21" class="java.lang.String" itemvalue="pydantic" />
            <item index="22" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="23" class="java.lang.String" itemvalue="pyglet" />
            <item index="24" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="25" class="java.lang.String" itemvalue="asgiref" />
            <item index="26" class="java.lang.String" itemvalue="vobject" />
            <item index="27" class="java.lang.String" itemvalue="ray" />
            <item index="28" class="java.lang.String" itemvalue="pandas-datareader" />
            <item index="29" class="java.lang.String" itemvalue="attrs" />
            <item index="30" class="java.lang.String" itemvalue="simplejson" />
            <item index="31" class="java.lang.String" itemvalue="prettytable" />
            <item index="32" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="33" class="java.lang.String" itemvalue="tensorboard" />
            <item index="34" class="java.lang.String" itemvalue="cymem" />
            <item index="35" class="java.lang.String" itemvalue="futu-api" />
            <item index="36" class="java.lang.String" itemvalue="imageio" />
            <item index="37" class="java.lang.String" itemvalue="platformdirs" />
            <item index="38" class="java.lang.String" itemvalue="SceneGraphParser" />
            <item index="39" class="java.lang.String" itemvalue="iopath" />
            <item index="40" class="java.lang.String" itemvalue="matplotlib" />
            <item index="41" class="java.lang.String" itemvalue="murmurhash" />
            <item index="42" class="java.lang.String" itemvalue="msgpack" />
            <item index="43" class="java.lang.String" itemvalue="yacs" />
            <item index="44" class="java.lang.String" itemvalue="rsa" />
            <item index="45" class="java.lang.String" itemvalue="decorator" />
            <item index="46" class="java.lang.String" itemvalue="wasabi" />
            <item index="47" class="java.lang.String" itemvalue="brotlipy" />
            <item index="48" class="java.lang.String" itemvalue="networkx" />
            <item index="49" class="java.lang.String" itemvalue="glfw" />
            <item index="50" class="java.lang.String" itemvalue="alpaca-trade-api" />
            <item index="51" class="java.lang.String" itemvalue="datasets" />
            <item index="52" class="java.lang.String" itemvalue="numpy" />
            <item index="53" class="java.lang.String" itemvalue="pyasn1" />
            <item index="54" class="java.lang.String" itemvalue="websocket-client" />
            <item index="55" class="java.lang.String" itemvalue="pyrsistent" />
            <item index="56" class="java.lang.String" itemvalue="kornia" />
            <item index="57" class="java.lang.String" itemvalue="mkl-fft" />
            <item index="58" class="java.lang.String" itemvalue="tensorflow" />
            <item index="59" class="java.lang.String" itemvalue="seaborn" />
            <item index="60" class="java.lang.String" itemvalue="tensorboard-plugin-wit" />
            <item index="61" class="java.lang.String" itemvalue="PyWavelets" />
            <item index="62" class="java.lang.String" itemvalue="smart-open" />
            <item index="63" class="java.lang.String" itemvalue="tenacity" />
            <item index="64" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="65" class="java.lang.String" itemvalue="websockets" />
            <item index="66" class="java.lang.String" itemvalue="pyarrow" />
            <item index="67" class="java.lang.String" itemvalue="PyOpenGL" />
            <item index="68" class="java.lang.String" itemvalue="scipy" />
            <item index="69" class="java.lang.String" itemvalue="Flask-Cors" />
            <item index="70" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="71" class="java.lang.String" itemvalue="opencv-python" />
            <item index="72" class="java.lang.String" itemvalue="lz4" />
            <item index="73" class="java.lang.String" itemvalue="plotly" />
            <item index="74" class="java.lang.String" itemvalue="et-xmlfile" />
            <item index="75" class="java.lang.String" itemvalue="tensorflow-io-gcs-filesystem" />
            <item index="76" class="java.lang.String" itemvalue="addict" />
            <item index="77" class="java.lang.String" itemvalue="langcodes" />
            <item index="78" class="java.lang.String" itemvalue="pandas" />
            <item index="79" class="java.lang.String" itemvalue="termcolor" />
            <item index="80" class="java.lang.String" itemvalue="caldav" />
            <item index="81" class="java.lang.String" itemvalue="openmim" />
            <item index="82" class="java.lang.String" itemvalue="toolz" />
            <item index="83" class="java.lang.String" itemvalue="Django" />
            <item index="84" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="85" class="java.lang.String" itemvalue="cachetools" />
            <item index="86" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="87" class="java.lang.String" itemvalue="multidict" />
            <item index="88" class="java.lang.String" itemvalue="responses" />
            <item index="89" class="java.lang.String" itemvalue="thinc" />
            <item index="90" class="java.lang.String" itemvalue="yarl" />
            <item index="91" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="92" class="java.lang.String" itemvalue="pytz" />
            <item index="93" class="java.lang.String" itemvalue="einops" />
            <item index="94" class="java.lang.String" itemvalue="Pillow" />
            <item index="95" class="java.lang.String" itemvalue="absl-py" />
            <item index="96" class="java.lang.String" itemvalue="protobuf" />
            <item index="97" class="java.lang.String" itemvalue="html5lib" />
            <item index="98" class="java.lang.String" itemvalue="joblib" />
            <item index="99" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="100" class="java.lang.String" itemvalue="opt-einsum" />
            <item index="101" class="java.lang.String" itemvalue="cycler" />
            <item index="102" class="java.lang.String" itemvalue="gast" />
            <item index="103" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="104" class="java.lang.String" itemvalue="frozenlist" />
            <item index="105" class="java.lang.String" itemvalue="fsspec" />
            <item index="106" class="java.lang.String" itemvalue="spacy" />
            <item index="107" class="java.lang.String" itemvalue="appdirs" />
            <item index="108" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="109" class="java.lang.String" itemvalue="oauthlib" />
            <item index="110" class="java.lang.String" itemvalue="accelerate" />
            <item index="111" class="java.lang.String" itemvalue="keras" />
            <item index="112" class="java.lang.String" itemvalue="pyparsing" />
            <item index="113" class="java.lang.String" itemvalue="commonmark" />
            <item index="114" class="java.lang.String" itemvalue="Markdown" />
            <item index="115" class="java.lang.String" itemvalue="model-index" />
            <item index="116" class="java.lang.String" itemvalue="xxhash" />
            <item index="117" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="118" class="java.lang.String" itemvalue="wincertstore" />
            <item index="119" class="java.lang.String" itemvalue="tifffile" />
            <item index="120" class="java.lang.String" itemvalue="AMP" />
            <item index="121" class="java.lang.String" itemvalue="h5py" />
            <item index="122" class="java.lang.String" itemvalue="wrapt" />
            <item index="123" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="124" class="java.lang.String" itemvalue="deprecation" />
            <item index="125" class="java.lang.String" itemvalue="pathy" />
            <item index="126" class="java.lang.String" itemvalue="catalogue" />
            <item index="127" class="java.lang.String" itemvalue="tensorflow-intel" />
            <item index="128" class="java.lang.String" itemvalue="fonttools" />
            <item index="129" class="java.lang.String" itemvalue="ema-pytorch" />
            <item index="130" class="java.lang.String" itemvalue="pip-search" />
            <item index="131" class="java.lang.String" itemvalue="virtualenv" />
            <item index="132" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="133" class="java.lang.String" itemvalue="distlib" />
            <item index="134" class="java.lang.String" itemvalue="gym" />
            <item index="135" class="java.lang.String" itemvalue="dask" />
            <item index="136" class="java.lang.String" itemvalue="mujoco" />
            <item index="137" class="java.lang.String" itemvalue="mkl-service" />
            <item index="138" class="java.lang.String" itemvalue="scikit-image" />
            <item index="139" class="java.lang.String" itemvalue="async-timeout" />
            <item index="140" class="java.lang.String" itemvalue="spacy-loggers" />
            <item index="141" class="java.lang.String" itemvalue="yapf" />
            <item index="142" class="java.lang.String" itemvalue="cloudpickle" />
            <item index="143" class="java.lang.String" itemvalue="wcwidth" />
            <item index="144" class="java.lang.String" itemvalue="spacy-legacy" />
            <item index="145" class="java.lang.String" itemvalue="sqlparse" />
            <item index="146" class="java.lang.String" itemvalue="preshed" />
            <item index="147" class="java.lang.String" itemvalue="requests-oauthlib" />
            <item index="148" class="java.lang.String" itemvalue="yfinance" />
            <item index="149" class="java.lang.String" itemvalue="gym-notices" />
            <item index="150" class="java.lang.String" itemvalue="blis" />
            <item index="151" class="java.lang.String" itemvalue="urllib3" />
            <item index="152" class="java.lang.String" itemvalue="Cython" />
            <item index="153" class="java.lang.String" itemvalue="Flask" />
            <item index="154" class="java.lang.String" itemvalue="coverage" />
            <item index="155" class="java.lang.String" itemvalue="nose" />
            <item index="156" class="java.lang.String" itemvalue="six" />
            <item index="157" class="java.lang.String" itemvalue="timm" />
            <item index="158" class="java.lang.String" itemvalue="typer" />
            <item index="159" class="java.lang.String" itemvalue="beartype" />
            <item index="160" class="java.lang.String" itemvalue="ordered-set" />
            <item index="161" class="java.lang.String" itemvalue="tzdata" />
            <item index="162" class="java.lang.String" itemvalue="rich" />
            <item index="163" class="java.lang.String" itemvalue="dill" />
            <item index="164" class="java.lang.String" itemvalue="einops-exts" />
            <item index="165" class="java.lang.String" itemvalue="labmaze" />
            <item index="166" class="java.lang.String" itemvalue="aiohttp" />
            <item index="167" class="java.lang.String" itemvalue="grpcio" />
            <item index="168" class="java.lang.String" itemvalue="frozendict" />
            <item index="169" class="java.lang.String" itemvalue="aiosignal" />
            <item index="170" class="java.lang.String" itemvalue="dm-tree" />
            <item index="171" class="java.lang.String" itemvalue="pycocotools" />
            <item index="172" class="java.lang.String" itemvalue="google-auth" />
            <item index="173" class="java.lang.String" itemvalue="openpyxl" />
            <item index="174" class="java.lang.String" itemvalue="tokenizers" />
            <item index="175" class="java.lang.String" itemvalue="transformers" />
            <item index="176" class="java.lang.String" itemvalue="sacremoses" />
            <item index="177" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="178" class="java.lang.String" itemvalue="cryptography" />
            <item index="179" class="java.lang.String" itemvalue="torch" />
            <item index="180" class="java.lang.String" itemvalue="mkl-random" />
            <item index="181" class="java.lang.String" itemvalue="requests" />
            <item index="182" class="java.lang.String" itemvalue="torchvision" />
            <item index="183" class="java.lang.String" itemvalue="click" />
            <item index="184" class="java.lang.String" itemvalue="tqdm" />
            <item index="185" class="java.lang.String" itemvalue="win-inet-pton" />
            <item index="186" class="java.lang.String" itemvalue="regex" />
            <item index="187" class="java.lang.String" itemvalue="filelock" />
            <item index="188" class="java.lang.String" itemvalue="PySocks" />
            <item index="189" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="190" class="java.lang.String" itemvalue="colorama" />
            <item index="191" class="java.lang.String" itemvalue="certifi" />
            <item index="192" class="java.lang.String" itemvalue="torchaudio" />
            <item index="193" class="java.lang.String" itemvalue="Jinja2" />
            <item index="194" class="java.lang.String" itemvalue="jsonschema" />
            <item index="195" class="java.lang.String" itemvalue="psutil" />
            <item index="196" class="java.lang.String" itemvalue="sktime" />
            <item index="197" class="java.lang.String" itemvalue="sktimm" />
            <item index="198" class="java.lang.String" itemvalue="tslearn" />
            <item index="199" class="java.lang.String" itemvalue="optuna" />
            <item index="200" class="java.lang.String" itemvalue="kaleido" />
            <item index="201" class="java.lang.String" itemvalue="transforms3d" />
            <item index="202" class="java.lang.String" itemvalue="av" />
            <item index="203" class="java.lang.String" itemvalue="pybullet" />
            <item index="204" class="java.lang.String" itemvalue="pika" />
            <item index="205" class="java.lang.String" itemvalue="ruamel.yaml" />
            <item index="206" class="java.lang.String" itemvalue="moviepy" />
            <item index="207" class="java.lang.String" itemvalue="dm_control" />
            <item index="208" class="java.lang.String" itemvalue="safetensors" />
            <item index="209" class="java.lang.String" itemvalue="pytorchvideo" />
            <item index="210" class="java.lang.String" itemvalue="einops_exts" />
            <item index="211" class="java.lang.String" itemvalue="PyJWT" />
            <item index="212" class="java.lang.String" itemvalue="openai" />
            <item index="213" class="java.lang.String" itemvalue="itchat-uos" />
            <item index="214" class="java.lang.String" itemvalue="flask_socketio" />
            <item index="215" class="java.lang.String" itemvalue="EdgeGPT" />
            <item index="216" class="java.lang.String" itemvalue="discord.py" />
            <item index="217" class="java.lang.String" itemvalue="flask" />
            <item index="218" class="java.lang.String" itemvalue="imagecorruptions" />
            <item index="219" class="java.lang.String" itemvalue="shapely" />
            <item index="220" class="java.lang.String" itemvalue="cityscapesscripts" />
            <item index="221" class="java.lang.String" itemvalue="terminaltables" />
            <item index="222" class="java.lang.String" itemvalue="mmengine" />
            <item index="223" class="java.lang.String" itemvalue="finnhub" />
            <item index="224" class="java.lang.String" itemvalue="pathlib" />
            <item index="225" class="java.lang.String" itemvalue="typing" />
            <item index="226" class="java.lang.String" itemvalue="xatlas" />
            <item index="227" class="java.lang.String" itemvalue="torch-ema" />
            <item index="228" class="java.lang.String" itemvalue="nvdiffrast" />
            <item index="229" class="java.lang.String" itemvalue="carvekit-colab" />
            <item index="230" class="java.lang.String" itemvalue="diffusers" />
            <item index="231" class="java.lang.String" itemvalue="PyMCubes" />
            <item index="232" class="java.lang.String" itemvalue="trimesh" />
            <item index="233" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="234" class="java.lang.String" itemvalue="debugpy-run" />
            <item index="235" class="java.lang.String" itemvalue="pymeshlab" />
            <item index="236" class="java.lang.String" itemvalue="gdown" />
            <item index="237" class="java.lang.String" itemvalue="taming-transformers-rom1504" />
            <item index="238" class="java.lang.String" itemvalue="dearpygui" />
            <item index="239" class="java.lang.String" itemvalue="lightning" />
            <item index="240" class="java.lang.String" itemvalue="xformers" />
            <item index="241" class="java.lang.String" itemvalue="libigl" />
            <item index="242" class="java.lang.String" itemvalue="jaxtyping" />
            <item index="243" class="java.lang.String" itemvalue="typeguard" />
            <item index="244" class="java.lang.String" itemvalue="bitsandbytes" />
            <item index="245" class="java.lang.String" itemvalue="nerfacc" />
            <item index="246" class="java.lang.String" itemvalue="gradio" />
            <item index="247" class="java.lang.String" itemvalue="peft" />
            <item index="248" class="java.lang.String" itemvalue="fire" />
            <item index="249" class="java.lang.String" itemvalue="fvcore" />
            <item index="250" class="java.lang.String" itemvalue="openfe" />
            <item index="251" class="java.lang.String" itemvalue="traitlets" />
            <item index="252" class="java.lang.String" itemvalue="jupyter-core" />
            <item index="253" class="java.lang.String" itemvalue="googleapis-common-protos" />
            <item index="254" class="java.lang.String" itemvalue="ptyprocess" />
            <item index="255" class="java.lang.String" itemvalue="blessings" />
            <item index="256" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="257" class="java.lang.String" itemvalue="dgl" />
            <item index="258" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="259" class="java.lang.String" itemvalue="redis" />
            <item index="260" class="java.lang.String" itemvalue="aiohttp-cors" />
            <item index="261" class="java.lang.String" itemvalue="ipdb" />
            <item index="262" class="java.lang.String" itemvalue="Pygments" />
            <item index="263" class="java.lang.String" itemvalue="zipp" />
            <item index="264" class="java.lang.String" itemvalue="graphviz" />
            <item index="265" class="java.lang.String" itemvalue="prompt-toolkit" />
            <item index="266" class="java.lang.String" itemvalue="anytree" />
            <item index="267" class="java.lang.String" itemvalue="google-api-core" />
            <item index="268" class="java.lang.String" itemvalue="parso" />
            <item index="269" class="java.lang.String" itemvalue="py-spy" />
            <item index="270" class="java.lang.String" itemvalue="gpustat" />
            <item index="271" class="java.lang.String" itemvalue="nbformat" />
            <item index="272" class="java.lang.String" itemvalue="ipython" />
            <item index="273" class="java.lang.String" itemvalue="google" />
            <item index="274" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="275" class="java.lang.String" itemvalue="prometheus-client" />
            <item index="276" class="java.lang.String" itemvalue="hiredis" />
            <item index="277" class="java.lang.String" itemvalue="opencensus" />
            <item index="278" class="java.lang.String" itemvalue="jedi" />
            <item index="279" class="java.lang.String" itemvalue="aioredis" />
            <item index="280" class="java.lang.String" itemvalue="retrying" />
            <item index="281" class="java.lang.String" itemvalue="opencensus-context" />
            <item index="282" class="java.lang.String" itemvalue="colorful" />
            <item index="283" class="java.lang.String" itemvalue="nvidia-ml-py3" />
            <item index="284" class="java.lang.String" itemvalue="black" />
            <item index="285" class="java.lang.String" itemvalue="qlib" />
            <item index="286" class="java.lang.String" itemvalue="inflect" />
            <item index="287" class="java.lang.String" itemvalue="httpx" />
            <item index="288" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="289" class="java.lang.String" itemvalue="tzlocal" />
            <item index="290" class="java.lang.String" itemvalue="open-clip-torch" />
            <item index="291" class="java.lang.String" itemvalue="validators" />
            <item index="292" class="java.lang.String" itemvalue="h11" />
            <item index="293" class="java.lang.String" itemvalue="deepdiff" />
            <item index="294" class="java.lang.String" itemvalue="gitdb" />
            <item index="295" class="java.lang.String" itemvalue="lightning-cloud" />
            <item index="296" class="java.lang.String" itemvalue="lmdb" />
            <item index="297" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="298" class="java.lang.String" itemvalue="markupsafe" />
            <item index="299" class="java.lang.String" itemvalue="easydict" />
            <item index="300" class="java.lang.String" itemvalue="semantic-version" />
            <item index="301" class="java.lang.String" itemvalue="starsessions" />
            <item index="302" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="303" class="java.lang.String" itemvalue="starlette" />
            <item index="304" class="java.lang.String" itemvalue="anyio" />
            <item index="305" class="java.lang.String" itemvalue="gitpython" />
            <item index="306" class="java.lang.String" itemvalue="uvicorn" />
            <item index="307" class="java.lang.String" itemvalue="sympy" />
            <item index="308" class="java.lang.String" itemvalue="inquirer" />
            <item index="309" class="java.lang.String" itemvalue="werkzeug" />
            <item index="310" class="java.lang.String" itemvalue="basicsr" />
            <item index="311" class="java.lang.String" itemvalue="dateutils" />
            <item index="312" class="java.lang.String" itemvalue="streamlit" />
            <item index="313" class="java.lang.String" itemvalue="orjson" />
            <item index="314" class="java.lang.String" itemvalue="pydub" />
            <item index="315" class="java.lang.String" itemvalue="gradio-client" />
            <item index="316" class="java.lang.String" itemvalue="altair" />
            <item index="317" class="java.lang.String" itemvalue="coloredlogs" />
            <item index="318" class="java.lang.String" itemvalue="contourpy" />
            <item index="319" class="java.lang.String" itemvalue="omegaconf" />
            <item index="320" class="java.lang.String" itemvalue="torch-fidelity" />
            <item index="321" class="java.lang.String" itemvalue="mdit-py-plugins" />
            <item index="322" class="java.lang.String" itemvalue="urwid" />
            <item index="323" class="java.lang.String" itemvalue="ninja" />
            <item index="324" class="java.lang.String" itemvalue="test-tube" />
            <item index="325" class="java.lang.String" itemvalue="pywavelets" />
            <item index="326" class="java.lang.String" itemvalue="uc-micro-py" />
            <item index="327" class="java.lang.String" itemvalue="ffmpy" />
            <item index="328" class="java.lang.String" itemvalue="hjson" />
            <item index="329" class="java.lang.String" itemvalue="readchar" />
            <item index="330" class="java.lang.String" itemvalue="pydeprecate" />
            <item index="331" class="java.lang.String" itemvalue="httpcore" />
            <item index="332" class="java.lang.String" itemvalue="tb-nightly" />
            <item index="333" class="java.lang.String" itemvalue="pydeck" />
            <item index="334" class="java.lang.String" itemvalue="pkgutil-resolve-name" />
            <item index="335" class="java.lang.String" itemvalue="pympler" />
            <item index="336" class="java.lang.String" itemvalue="smmap" />
            <item index="337" class="java.lang.String" itemvalue="imageio-ffmpeg" />
            <item index="338" class="java.lang.String" itemvalue="antlr4-python3-runtime" />
            <item index="339" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="340" class="java.lang.String" itemvalue="python-editor" />
            <item index="341" class="java.lang.String" itemvalue="imgaug" />
            <item index="342" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="343" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="344" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="345" class="java.lang.String" itemvalue="deepspeed" />
            <item index="346" class="java.lang.String" itemvalue="lightning-utilities" />
            <item index="347" class="java.lang.String" itemvalue="mdurl" />
            <item index="348" class="java.lang.String" itemvalue="markdown" />
            <item index="349" class="java.lang.String" itemvalue="wandb" />
            <item index="350" class="java.lang.String" itemvalue="tomli" />
            <item index="351" class="java.lang.String" itemvalue="backports-zoneinfo" />
            <item index="352" class="java.lang.String" itemvalue="linkify-it-py" />
            <item index="353" class="java.lang.String" itemvalue="onnx" />
            <item index="354" class="java.lang.String" itemvalue="pygments" />
            <item index="355" class="java.lang.String" itemvalue="blinker" />
            <item index="356" class="java.lang.String" itemvalue="croniter" />
            <item index="357" class="java.lang.String" itemvalue="tornado" />
            <item index="358" class="java.lang.String" itemvalue="aiofiles" />
            <item index="359" class="java.lang.String" itemvalue="blessed" />
            <item index="360" class="java.lang.String" itemvalue="packaging" />
            <item index="361" class="java.lang.String" itemvalue="lazy-loader" />
            <item index="362" class="java.lang.String" itemvalue="python-multipart" />
            <item index="363" class="java.lang.String" itemvalue="humanfriendly" />
            <item index="364" class="java.lang.String" itemvalue="toml" />
            <item index="365" class="java.lang.String" itemvalue="pytz-deprecation-shim" />
            <item index="366" class="java.lang.String" itemvalue="docker-pycreds" />
            <item index="367" class="java.lang.String" itemvalue="albumentations" />
            <item index="368" class="java.lang.String" itemvalue="fastapi" />
            <item index="369" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="370" class="java.lang.String" itemvalue="pathtools" />
            <item index="371" class="java.lang.String" itemvalue="watchdog" />
            <item index="372" class="java.lang.String" itemvalue="future" />
            <item index="373" class="java.lang.String" itemvalue="mpmath" />
            <item index="374" class="java.lang.String" itemvalue="pudb" />
            <item index="375" class="java.lang.String" itemvalue="nvidia-htop" />
            <item index="376" class="java.lang.String" itemvalue="invisible-watermark" />
            <item index="377" class="java.lang.String" itemvalue="pyjwt" />
            <item index="378" class="java.lang.String" itemvalue="setproctitle" />
            <item index="379" class="java.lang.String" itemvalue="open_flamingo" />
            <item index="380" class="java.lang.String" itemvalue="open-flamingo" />
            <item index="381" class="java.lang.String" itemvalue="ffmpeg" />
            <item index="382" class="java.lang.String" itemvalue="opencv_python" />
            <item index="383" class="java.lang.String" itemvalue="catboost" />
            <item index="384" class="java.lang.String" itemvalue="lightgbm" />
            <item index="385" class="java.lang.String" itemvalue="xgboost" />
            <item index="386" class="java.lang.String" itemvalue="cmake" />
            <item index="387" class="java.lang.String" itemvalue="chromadb" />
            <item index="388" class="java.lang.String" itemvalue="tiktoken" />
            <item index="389" class="java.lang.String" itemvalue="minecraft_launcher_lib" />
            <item index="390" class="java.lang.String" itemvalue="gymnasium" />
            <item index="391" class="java.lang.String" itemvalue="langchain" />
            <item index="392" class="java.lang.String" itemvalue="cchardet" />
            <item index="393" class="java.lang.String" itemvalue="javascript" />
            <item index="394" class="java.lang.String" itemvalue="ahk" />
            <item index="395" class="java.lang.String" itemvalue="backoff" />
            <item index="396" class="java.lang.String" itemvalue="pillow" />
            <item index="397" class="java.lang.String" itemvalue="pydirectinput" />
            <item index="398" class="java.lang.String" itemvalue="segment-anything" />
            <item index="399" class="java.lang.String" itemvalue="mmocr" />
            <item index="400" class="java.lang.String" itemvalue="chardet" />
            <item index="401" class="java.lang.String" itemvalue="fastdtw" />
            <item index="402" class="java.lang.String" itemvalue="celery" />
            <item index="403" class="java.lang.String" itemvalue="Multi-Template-Matching" />
            <item index="404" class="java.lang.String" itemvalue="scikit-learn-extra" />
            <item index="405" class="java.lang.String" itemvalue="easyocr" />
            <item index="406" class="java.lang.String" itemvalue="torchcde" />
            <item index="407" class="java.lang.String" itemvalue="gensim" />
            <item index="408" class="java.lang.String" itemvalue="pymysql" />
            <item index="409" class="java.lang.String" itemvalue="cvxpy" />
            <item index="410" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="411" class="java.lang.String" itemvalue="cvxopt" />
            <item index="412" class="java.lang.String" itemvalue="setuptools" />
            <item index="413" class="java.lang.String" itemvalue="rqalpha" />
            <item index="414" class="java.lang.String" itemvalue="jsonlines" />
            <item index="415" class="java.lang.String" itemvalue="opencv-contrib-python" />
            <item index="416" class="java.lang.String" itemvalue="fastparquet" />
            <item index="417" class="java.lang.String" itemvalue="ta" />
            <item index="418" class="java.lang.String" itemvalue="pyecharts" />
            <item index="419" class="java.lang.String" itemvalue="pandas_ta" />
            <item index="420" class="java.lang.String" itemvalue="snapshot_selenium" />
            <item index="421" class="java.lang.String" itemvalue="pandas_market_calendars" />
            <item index="422" class="java.lang.String" itemvalue="langchain_community" />
            <item index="423" class="java.lang.String" itemvalue="rotary_embedding_torch" />
            <item index="424" class="java.lang.String" itemvalue="dataclass_wizard" />
            <item index="425" class="java.lang.String" itemvalue="pynvml" />
            <item index="426" class="java.lang.String" itemvalue="segment_anything" />
            <item index="427" class="java.lang.String" itemvalue="vit_pytorch" />
            <item index="428" class="java.lang.String" itemvalue="supervision" />
            <item index="429" class="java.lang.String" itemvalue="mamba-ssm" />
            <item index="430" class="java.lang.String" itemvalue="torchdict" />
            <item index="431" class="java.lang.String" itemvalue="math_verify" />
            <item index="432" class="java.lang.String" itemvalue="vllm" />
            <item index="433" class="java.lang.String" itemvalue="pre-commit" />
            <item index="434" class="java.lang.String" itemvalue="distributed" />
            <item index="435" class="java.lang.String" itemvalue="pyav" />
            <item index="436" class="java.lang.String" itemvalue="pandarallel" />
            <item index="437" class="java.lang.String" itemvalue="litellm" />
            <item index="438" class="java.lang.String" itemvalue="flash-attn" />
            <item index="439" class="java.lang.String" itemvalue="hydra-core" />
            <item index="440" class="java.lang.String" itemvalue="liger-kernel" />
            <item index="441" class="java.lang.String" itemvalue="tensordict" />
            <item index="442" class="java.lang.String" itemvalue="codetiming" />
            <item index="443" class="java.lang.String" itemvalue="pybind11" />
            <item index="444" class="java.lang.String" itemvalue="pylatexenc" />
            <item index="445" class="java.lang.String" itemvalue="torchdata" />
            <item index="446" class="java.lang.String" itemvalue="pebble" />
            <item index="447" class="java.lang.String" itemvalue="evalplus" />
            <item index="448" class="java.lang.String" itemvalue="fraction" />
            <item index="449" class="java.lang.String" itemvalue="trl" />
            <item index="450" class="java.lang.String" itemvalue="adbutils" />
            <item index="451" class="java.lang.String" itemvalue="diskcache" />
            <item index="452" class="java.lang.String" itemvalue="PyPDF2" />
            <item index="453" class="java.lang.String" itemvalue="unidiff" />
            <item index="454" class="java.lang.String" itemvalue="boto3" />
            <item index="455" class="java.lang.String" itemvalue="browsergym" />
            <item index="456" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="457" class="java.lang.String" itemvalue="pytest" />
            <item index="458" class="java.lang.String" itemvalue="html2text" />
            <item index="459" class="java.lang.String" itemvalue="pytest-asyncio" />
            <item index="460" class="java.lang.String" itemvalue="mcp" />
            <item index="461" class="java.lang.String" itemvalue="loguru" />
            <item index="462" class="java.lang.String" itemvalue="docker" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="str.replace_with" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>